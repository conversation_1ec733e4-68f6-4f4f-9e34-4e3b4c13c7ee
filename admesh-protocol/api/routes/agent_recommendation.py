from typing import Dict, Any
from fastapi import HTT<PERSON><PERSON>xception
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from firebase_admin import firestore
from firebase.config import get_db
from auth.api_key import verify_api_key_from_request
import time
import logging
import uuid
import json
from api.routes.click import build_admesh_link
from api.routes.openrouter import call_openrouter, clean_response
from api.utils.embedding import embed_text, cosine_similarity

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

# Default model to use for all LLM calls
DEFAULT_MODEL = "mistralai/mistral-7b-instruct"

# ------------------------
# Request + Response Models
# ------------------------


class AgentRecommendRequest(BaseModel):
    query: str = Field(..., description="The user's query")
    format: Optional[str] = Field(default="auto")
    previous_query: Optional[str] = None
    previous_summary: Optional[str] = None
    session_id: Optional[str] = None


class AgentRecommendation(BaseModel):
    title: str
    reason: str
    intent_match_score: float
    admesh_link: str
    ad_id: str
    product_id: str

    # Additional product/offer fields
    url: Optional[str] = ""
    redirect_url: Optional[str] = ""
    description: Optional[str] = ""
    pricing: Optional[str] = ""
    reward_note: Optional[str] = ""
    keywords: Optional[List[str]] = []
    categories: Optional[List[str]] = []
    features: Optional[List[str]] = []
    integrations: Optional[List[str]] = []
    has_free_tier: Optional[bool] = False
    trial_days: Optional[int] = 0
    audience_segment: Optional[str] = ""
    is_ai_powered: Optional[bool] = False
    is_open_source: Optional[bool] = False
    offer_trust_score: Optional[float] = 0.5
    brand_trust_score: Optional[float] = 0.5
    is_fallback: Optional[bool] = False


class AgentFollowupSuggestion(BaseModel):
    label: str
    query: str
    product_mentions: List[str]
    admesh_links: Dict[str, str]


class AgentRecommendResponse(BaseModel):
    session_id: str
    intent: Dict[str, Any]
    response: Dict[str, Any]
    tokens_used: int
    model_used: str
    recommendation_id: Optional[str] = None
    end_of_session: Optional[bool] = True

# ------------------------
# POST /recommend
# ------------------------


@router.post("/recommend", response_model=AgentRecommendResponse)
async def handle_agent_recommendation(
    payload: AgentRecommendRequest,
    request: Request
):
    try:
        # --- Step 0: Authenticate agent via API key ---
        try:
            api_key_info = verify_api_key_from_request(request)
            agent_id = api_key_info["agent_id"]
            is_test = api_key_info.get("is_test", False)
            logger.info(
                f"🔑 API key verified: agent_id={agent_id}, is_test={is_test}")
        except HTTPException as e:
            logger.error(f"Authentication error: {e.detail}")
            raise e
        except Exception as e:
            logger.exception(
                f"Unexpected error during authentication: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Internal server error")

        # --- Step 1: Generate session ID ---
        session_id = payload.session_id or f"sess_{int(time.time())}_{agent_id[:6]}"

        # --- Step 2: Detect intent from query ---
        intent = detect_user_intent(payload.query, DEFAULT_MODEL)

        if intent.get("llm_intent_confidence_score", 0) < 0.5:
            raise HTTPException(
                status_code=400, detail="Low intent confidence")

        categories = intent.get("categories")
        keywords = intent.get("keywords", [])
        print("Agent Recomm - categories", categories)
        print("Agent Recomm - keywords", keywords)
        llm_confidence = intent.get("llm_intent_confidence_score", 0.5)

        # --- Step 2.5: Generate query embedding for semantic matching ---
        query_embedding = embed_text(intent["goal"])
        logger.info(f"🔮 Generated query embedding with {len(query_embedding)} dimensions")

        # --- Step 3: Fetch and score matching offers ---
        scored_offers = fetch_active_offers(
            categories=categories,
            keywords=keywords,
            llm_confidence=llm_confidence,
            query_embedding=query_embedding,
            is_test=is_test  # Pass the is_test flag to track views correctly
        )

        # --- Step 3.5: Fallback mechanism if no relevant offers found ---
        if not scored_offers:
            logger.info("🔄 No matching offers found, attempting fallback to any active offer")
            fallback_offer = fetch_fallback_offer(is_test=is_test)
            if fallback_offer:
                scored_offers = [fallback_offer]
                logger.info(f"✅ Using fallback offer: {fallback_offer.get('title', 'Unknown')}")
            else:
                logger.warning("⚠️ No fallback offers available")

        # --- Step 4: Build enriched recommendations with tracking links ---
        recommendations = []
        recommendation_id = str(uuid.uuid4())
        recommendation_mappings = []

        for item in scored_offers:
            offer_id = item.get("offer_id")
            pid = item.get("product_id")
            redirect_url = item.get("redirect_url") or item.get("url", "")

            ad_id = offer_id or str(uuid.uuid4())
            admesh_link = build_admesh_link(
                ad_id=ad_id, product_id=pid, redirect_url=redirect_url)

            # Ensure intent_match_score is between 0 and 1
            intent_match_score = min(
                max(item.get("intent_match_score", 0), 0), 1)

            # Build enriched recommendation with all available fields
            recommendation = {
                "title": item.get("title", ""),
                "reason": item.get("match_reason", ""),
                "intent_match_score": intent_match_score,
                "ad_id": ad_id,
                "product_id": pid,
                "admesh_link": admesh_link,

                # Additional product/offer fields
                "url": item.get("url", ""),
                "redirect_url": redirect_url,
                "description": item.get("description", ""),
                "pricing": item.get("pricing", ""),
                "reward_note": item.get("reward_note", ""),
                "keywords": item.get("keywords", []),
                "categories": item.get("categories", []),
                "features": item.get("features", []),
                "integrations": item.get("integration_list", []),
                "has_free_tier": item.get("has_free_tier", False),
                "trial_days": item.get("trial_days", 0),
                "audience_segment": item.get("audience_segment", ""),
                "is_ai_powered": item.get("is_ai_powered", False),
                "is_open_source": item.get("is_open_source", False),
                "offer_trust_score": item.get("offer_trust_score", 0.5),
                "brand_trust_score": item.get("brand_trust_score", 0.5),
                "is_fallback": item.get("is_fallback", False)
            }

            recommendations.append(recommendation)

            recommendation_mappings.append({
                "product_id": pid,
                "ad_id": ad_id,
                "admesh_link": admesh_link,
                "title": item.get("title", ""),
                "url": item.get("url", ""),
                "redirect_url": redirect_url
            })

        # --- Step 5: Log session and recommendations ---
        save_query_session(
            query=payload.query,
            intent=intent,
            model=DEFAULT_MODEL,
            agent_id=agent_id,
            user_id=None,
            session_id=session_id
        )

        db.collection("recommendations").add({
            "recommendation_id": recommendation_id,
            "session_id": session_id,
            "agent_id": agent_id,
            "query": payload.query,
            "products": recommendation_mappings,
            "created_at": firestore.SERVER_TIMESTAMP
        })

        # --- Step 6: Generate follow-up suggestions ---
        followups = build_followup_suggestions(
            intent, recommendations, session_id)

        # --- Step 7: Return API response ---
        # Check if we're using fallback offers
        is_fallback_response = any(rec.get("is_fallback", False) for rec in recommendations)

        return {
            "session_id": session_id,
            "intent": intent,
            "response": {
                "summary": summarize_detected_intent(intent, is_fallback=is_fallback_response),
                "recommendations": recommendations,
                "followup_suggestions": followups,
                "is_fallback": is_fallback_response
            },
            "tokens_used": 500,  # Placeholder until token tracking is implemented
            "model_used": DEFAULT_MODEL,
            "recommendation_id": recommendation_id,
            "end_of_session": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in /recommend: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


def compute_llm_confidence_score(intent: Dict[str, Any], query: str) -> float:
    """
    Compute a confidence score based on how well the extracted intent aligns with the query.
    """
    score = 0.0

    # 1. Categories match
    categories = intent.get("categories", [])
    if isinstance(categories, list) and categories:
        # Check if any category is in the query
        if any(cat.lower() in query.lower() for cat in categories if isinstance(cat, str)):
            score += 0.3

    # 2. Known mentions match
    mentions = intent.get("known_mentions", [])
    if isinstance(mentions, list) and any(m.lower() in query.lower() for m in mentions):
        score += 0.3

    # 3. Goal is descriptive enough
    if len(intent.get("goal", "").strip()) >= 15:
        score += 0.3

    return min(round(score + 0.1, 2), 1.0)


def detect_user_intent(query: str, model: str) -> Dict[str, Any]:
    """
    Detects user intent from a query using an LLM, normalizes and scores the result,
    and stores new intent types into Firestore if not already present.
    """
    intent_prompt = f"""
You are AdMesh, an AI assistant that helps users discover and evaluate the best software and tools.

Your task is to analyze the user's query and return their intent in structured JSON format — without recommending any specific products.

User query: "{query}"

Return a valid JSON object with the following keys:
{{
  "intent_group": "transactional",
  "type": "product_discovery",
  "categories": [...],              // List of relevant product categories (e.g. "ad_network", "crm", "analytics", etc.)
  "known_mentions": [...],          // List of product/brand names mentioned in the query
  "keywords": [...],                // Key terms in the query that help match offers
  "goal": "...",                    // A plain English explanation of what the user wants
  "llm_intent_confidence_score": 0.85
}}

Output only valid JSON. Do not include any text, comments, or markdown. Your response must be parseable by a JSON parser.
"""


    try:
        # Step 1: Call LLM
        raw = call_openrouter(intent_prompt, model)
        cleaned = clean_response(raw)
        intent = json.loads(cleaned)

        # Step 2: Validate expected fields and provide defaults if missing
        required_fields = ["type", "intent_group",
                           "categories", "goal", "known_mentions"]

        # Check if any required fields are missing and provide defaults
        if not all(field in intent for field in required_fields):
            logger.warning(f"Missing required fields in intent: {intent}")

            # Create default values for missing fields
            intent_type = intent.get("type", intent.get(
                "intent_type", "product_discovery"))
            intent_group = intent.get("intent_group", "commercial")

            # Get categories or use default
            categories = intent.get("categories", ["software"])
            goal = intent.get("goal", query)
            known_mentions = intent.get("known_mentions", [])
            keywords = intent.get("keywords", [])
        else:
            intent_type = intent.get("type", intent.get(
                "intent_type", "product_discovery"))
            intent_group = intent["intent_group"]
            categories = intent["categories"]
            goal = intent["goal"]
            known_mentions = intent["known_mentions"]
            keywords = intent.get("keywords", [])

        # Step 3: Normalize fields
        intent_type = intent_type.strip().lower().replace(" ", "_")
        intent_group = intent_group.strip().lower().replace(" ", "_")

        # Ensure categories is a list and normalize values
        if not isinstance(categories, list):
            categories = ["software"]  # Default category
        else:
            categories = [c.strip().lower().replace(" ", "_")
                          for c in categories if isinstance(c, str)]
            if not categories:  # If list is empty after filtering
                categories = ["software"]

        goal = goal.strip()
        known_mentions = known_mentions if isinstance(
            known_mentions, list) else []

        # Step 3.5: Enhanced keyword extraction with fallback logic
        from itertools import chain

        def generate_phrases(text: str) -> list[str]:
            """Generate n-grams (1-3 words) from the input text"""
            tokens = text.lower().split()
            phrases = []
            for n in range(1, min(4, len(tokens)+1)):  # unigrams to trigrams
                phrases.extend(" ".join(tokens[i:i+n])
                               for i in range(len(tokens)-n+1))
            return phrases

        # Combine LLM-provided + query-derived keywords
        llm_keywords = intent.get("keywords", [])
        query_phrases = generate_phrases(query)

        # Use known whitelist or simple filters to eliminate noise
        BAD_KEYWORDS = {"best", "top", "cheap", "buy", "free", "good", "great", "find", "looking", "for", "need", "want", "help", "me", "i", "a", "an", "the", "is", "are", "and", "or", "but", "with", "without"}

        # Filter out junk and deduplicate
        keywords = list({
            kw.strip().lower()
            for kw in chain(llm_keywords, query_phrases)
            if isinstance(kw, str) and kw.lower() not in BAD_KEYWORDS and len(kw) > 2
        })

        # Sort by length (longer phrases first) for better matching
        keywords = sorted(keywords, key=len, reverse=True)

        logger.info(f"🔍 Extracted keywords: {keywords}")
        logger.info(f"🔍 Original LLM keywords: {llm_keywords}")
        logger.info(f"🔍 Query phrases: {query_phrases}")

        # Step 4: Validate intent_group
        valid_groups = {"informational", "navigational",
                        "commercial", "transactional"}
        if intent_group not in valid_groups:
            intent_group = "commercial"

        # Step 5: Score fallback
        confidence_score = intent.get("llm_intent_confidence_score")
        if not isinstance(confidence_score, (int, float)):
            confidence_score = compute_llm_confidence_score({
                "categories": categories,
                "goal": goal,
                "known_mentions": known_mentions
            }, query)

        confidence_score = max(0.0, min(float(confidence_score), 1.0))

        # Step 6: Store intent_type if new
        intent_doc = db.collection("intent_types").document(intent_type).get()
        if not intent_doc.exists:
            db.collection("intent_types").document(intent_type).set({
                "intent_group": intent_group,
                "categories": categories,  # Store as a list
                "goal_examples": [goal],
                "source": "auto-added",
                "created_at": firestore.SERVER_TIMESTAMP
            })
            logger.info(f"🆕 Added new intent_type: {intent_type}")
        print("categories", categories)
        print("goal", goal)
        print("known_mentions", known_mentions)
        print("keywords", keywords)
        print("confidence_score", confidence_score)
        print("intent_group", intent_group)
        print("intent_type", intent_type)
        print("raw", raw)
        print("cleaned", cleaned)
        print("intent", intent)
        # Step 7: Return intent object
        return {
            "intent_group": intent_group,
            "intent_type": intent_type,
            "categories": categories,  # Return as a list
            "goal": goal,
            "known_mentions": known_mentions,
            "keywords": keywords,
            "llm_intent_confidence_score": round(confidence_score, 4)
        }

    except Exception as e:
        logger.exception(f"❌ Failed to detect intent: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to detect intent from query")


def fetch_active_offers(categories: List[str], keywords: List[str], llm_confidence: float, query_embedding: List[float], is_test: bool = False) -> List[Dict[str, Any]]:
    """
    Fetches active offers matching categories and keywords, then scores them using:
    - category match
    - keyword overlap
    - semantic similarity (using embeddings)
    - llm confidence
    - payout
    - trust scores

    Args:
        categories: List of categories to match
        keywords: List of keywords to match
        llm_confidence: Confidence score from the LLM
        query_embedding: Embedding vector for the user query
        is_test: Whether this is a test request (determines which view counter to increment)
    """
    logger.info(
        f"🔍 Starting fetch_active_offers with categories={categories}, keywords={keywords}, llm_confidence={llm_confidence}, is_test={is_test}")

    offers_ref = db.collection("offers")
    query_ref = offers_ref.where("active", "==", True)
    logger.info(f"📊 Initial query: active=True")

    # If we have categories, filter offers that contain any of these categories
    if categories and len(categories) > 0:
        # Use the first category for filtering
        primary_category = categories[0]
        query_ref = query_ref.where(
            "categories", "array_contains", primary_category)
        logger.info(
            f"📊 Added category filter: categories array_contains {primary_category}")

    offers = []
    logger.info(f"🔄 Starting to process query results")

    for doc in query_ref.stream():
        offer_id = doc.id
        logger.info(f"📝 Processing offer: {offer_id}")

        offer = doc.to_dict()
        if not offer:
            logger.warning(f"⚠️ Offer {offer_id} has no data, skipping")
            continue

        product_id = offer.get("product_id")
        if not product_id:
            logger.warning(f"⚠️ Offer {offer_id} has no product_id, skipping")
            continue
        logger.info(f"🔗 Found product_id: {product_id} for offer: {offer_id}")

        product_doc = db.collection("products").document(product_id).get()
        if not product_doc.exists:
            logger.warning(
                f"⚠️ Product {product_id} does not exist, skipping offer: {offer_id}")
            continue
        logger.info(f"✅ Found product document for product_id: {product_id}")

        product = product_doc.to_dict()
        logger.info(
            f"📋 Product data retrieved for {product_id}: {product.get('title', 'No Title')}")

        # --- Semantic Matching: Generate or retrieve product embedding ---
        product_text = f"{product.get('title', '')}. {product.get('description', '')}"
        product_embedding = product.get("embedding")

        if not product_embedding:
            logger.info(f"🔮 Generating new embedding for product: {product_id}")
            product_embedding = embed_text(product_text)
            # Cache the embedding in the product document
            db.collection("products").document(product_id).update({
                "embedding": product_embedding
            })
            logger.info(f"💾 Cached embedding for product: {product_id}")
        else:
            logger.info(f"✅ Using cached embedding for product: {product_id}")

        offer_keywords = set(offer.get("keywords", []))
        logger.info(f"🏷️ Offer keywords: {offer_keywords}")

        matched_keywords = offer_keywords.intersection(keywords)
        logger.info(f"🔍 Matched keywords: {matched_keywords}")

        keyword_score = len(matched_keywords) / \
            len(set(keywords)) if keywords else 0
        logger.info(f"📊 Raw keyword score: {keyword_score}")

        # Scoring components
        logger.info(f"🧮 Starting scoring calculations for offer: {offer_id}")

        # Calculate category match score based on overlap between offer categories and intent categories
        offer_categories = offer.get("categories", [])
        if not isinstance(offer_categories, list):
            offer_categories = [offer_categories] if offer_categories else []

        # Convert all categories to lowercase for case-insensitive comparison
        offer_categories_lower = [c.lower() if isinstance(
            c, str) else c for c in offer_categories]
        categories_lower = [c.lower() if isinstance(c, str)
                            else c for c in categories]

        # Calculate overlap between offer categories and intent categories
        matching_categories = set(
            offer_categories_lower).intersection(set(categories_lower))
        category_score = 0.2 * (len(matching_categories) /
                                max(len(categories), 1)) if matching_categories else 0.0

        logger.info(
            f"📊 Category score: {category_score} (matched {len(matching_categories)} of {len(categories)} categories)")

        keyword_score_scaled = round(0.2 * keyword_score, 4)
        logger.info(f"📊 Keyword score (scaled): {keyword_score_scaled}")

        # Calculate semantic similarity score
        semantic_score = cosine_similarity(query_embedding, product_embedding)
        semantic_score_scaled = round(0.3 * min(semantic_score, 1.0), 4)
        logger.info(f"📊 Semantic similarity: {semantic_score:.4f}, scaled: {semantic_score_scaled}")

        confidence_score = round(0.2 * min(max(llm_confidence, 0.0), 1.0), 4)
        logger.info(f"📊 Confidence score: {confidence_score}")

        # Handle payout which can be a number or a dictionary
        payout = offer.get("payout", 0)
        logger.info(
            f"💰 Raw payout data: {payout} (type: {type(payout).__name__})")

        if isinstance(payout, dict):
            # If payout is a dictionary, extract the amount
            logger.info(f"💰 Payout is a dictionary: {payout}")
            payout_amount = float(payout.get("amount", 0))
            logger.info(f"💰 Extracted payout amount: {payout_amount}")
        else:
            # If payout is a number or string, convert it to float
            logger.info(f"💰 Payout is a {type(payout).__name__}: {payout}")
            payout_amount = float(payout)
            logger.info(f"💰 Converted payout to float: {payout_amount}")

        cpa_score = round(0.2 * min(payout_amount / 100, 1.0), 4)
        logger.info(f"📊 CPA score: {cpa_score}")

        trust_offer = float(offer.get("offer_trust_score", 0.5))
        logger.info(f"📊 Offer trust score: {trust_offer}")

        trust_brand = float(offer.get("brand_trust_score", 0.5))
        logger.info(f"📊 Brand trust score: {trust_brand}")

        offer_trust_score = round(0.2 * ((trust_offer + trust_brand) / 2), 4)
        logger.info(f"📊 Combined offer trust score: {offer_trust_score}")

        # Sum all component scores (including semantic similarity)
        raw_score = category_score + keyword_score_scaled + \
            semantic_score_scaled + confidence_score + cpa_score + offer_trust_score

        # Normalize to ensure it's between 0 and 1
        # Since we have 6 components with varying max values, normalize to 1.0
        final_score = round(min(raw_score, 1.0), 4)

        logger.info(
            f"📊 Raw score sum: {raw_score}, Normalized final score: {final_score}")

        # Create user-friendly match reason instead of debug info
        reason_parts = []

        if matching_categories:
            category_names = [c.replace('_', ' ').title() for c in matching_categories]
            reason_parts.append(f"matches your {', '.join(category_names)} needs")

        if matched_keywords:
            keyword_list = list(matched_keywords)[:2]  # Show top 2 matched keywords
            reason_parts.append(f"relevant for {', '.join(keyword_list)}")

        if semantic_score > 0.85:
            reason_parts.append("highly relevant to your query")

        if trust_offer >= 0.8:  # High trust
            reason_parts.append("highly trusted platform")

        # Fallback reason if no specific matches
        if not reason_parts:
            reason_parts.append("recommended based on your query")

        match_reason = f"This offer {' and '.join(reason_parts)}."

        # Keep debug info in logs
        debug_reason = f"category={category_score}, keywords={keyword_score_scaled}, semantic={semantic_score_scaled}, confidence={confidence_score}, payout=${payout_amount:.2f}, cpa={cpa_score}, trust={offer_trust_score}"
        logger.info(f"📝 Debug match reason: {debug_reason}")
        logger.info(f"👤 User-friendly reason: {match_reason}")

        # Increment offer_views counter
        try:
            if is_test:
                db.collection("offers").document(doc.id).update({
                    "offer_views.test": firestore.Increment(1),
                    "offer_views.total": firestore.Increment(1)
                })
                logger.info(
                    f"📊 Incremented test view count for offer {offer_id}")
            else:
                db.collection("offers").document(doc.id).update({
                    "offer_views.production": firestore.Increment(1),
                    "offer_views.total": firestore.Increment(1)
                })
                logger.info(
                    f"📊 Incremented production view count for offer {offer_id}")
        except Exception as e:
            logger.error(
                f"❌ Failed to increment offer_views for offer {offer_id}: {str(e)}")

        offers.append({
            **offer,
            **product,
            "offer_id": doc.id,
            "product_id": product_id,
            "intent_match_score": final_score,
            "match_reason": match_reason
        })
        logger.info(
            f"✅ Added offer {offer_id} to results with score {final_score}")

    sorted_offers = sorted(
        offers, key=lambda x: x["intent_match_score"], reverse=True)[:5]
    logger.info(
        f"🏁 Returning top {len(sorted_offers)} offers from {len(offers)} total matches")

    # Log the top offers
    for i, offer in enumerate(sorted_offers):
        logger.info(
            f"🏆 Rank #{i+1}: {offer.get('title', 'No Title')} - Score: {offer.get('intent_match_score')}")

    return sorted_offers


def fetch_fallback_offer(is_test: bool = False) -> Optional[Dict[str, Any]]:
    """
    Fetches a single active offer as a fallback when no relevant matches are found.
    Returns the offer with the highest trust score and payout.

    Args:
        is_test: Whether this is a test request (determines which view counter to increment)

    Returns:
        A single offer dict with fallback scoring, or None if no active offers exist
    """
    logger.info("🔄 Starting fallback offer fetch")

    try:
        # Query for any active offers, ordered by trust score and payout
        offers_ref = db.collection("offers")
        query_ref = offers_ref.where("active", "==", True).limit(10)  # Get top 10 to choose from

        fallback_candidates = []

        for doc in query_ref.stream():
            offer_id = doc.id
            offer = doc.to_dict()

            if not offer:
                continue

            product_id = offer.get("product_id")
            if not product_id:
                continue

            # Get product data
            product_doc = db.collection("products").document(product_id).get()
            if not product_doc.exists:
                continue

            product = product_doc.to_dict()

            # Calculate fallback score based on trust and payout only
            trust_offer = float(offer.get("offer_trust_score", 0.5))
            trust_brand = float(offer.get("brand_trust_score", 0.5))
            combined_trust = (trust_offer + trust_brand) / 2

            # Handle payout
            payout = offer.get("payout", 0)
            if isinstance(payout, dict):
                payout_amount = float(payout.get("amount", 0))
            else:
                payout_amount = float(payout)

            # Simple fallback scoring: 70% trust, 30% payout (normalized to max $100)
            fallback_score = (0.7 * combined_trust) + (0.3 * min(payout_amount / 100, 1.0))

            fallback_candidates.append({
                **offer,
                **product,
                "offer_id": offer_id,
                "product_id": product_id,
                "intent_match_score": round(fallback_score, 4),
                "match_reason": "This offer is available in our inventory as a fallback recommendation. While it may not directly match your specific query, it represents a trusted option from our available offers.",
                "is_fallback": True
            })

        if not fallback_candidates:
            logger.warning("⚠️ No active offers found for fallback")
            return None

        # Sort by fallback score and take the best one
        best_fallback = max(fallback_candidates, key=lambda x: x["intent_match_score"])

        # Increment view counter for the selected fallback offer
        try:
            if is_test:
                db.collection("offers").document(best_fallback["offer_id"]).update({
                    "offer_views.test": firestore.Increment(1),
                    "offer_views.total": firestore.Increment(1)
                })
                logger.info(f"📊 Incremented test view count for fallback offer {best_fallback['offer_id']}")
            else:
                db.collection("offers").document(best_fallback["offer_id"]).update({
                    "offer_views.production": firestore.Increment(1),
                    "offer_views.total": firestore.Increment(1)
                })
                logger.info(f"📊 Incremented production view count for fallback offer {best_fallback['offer_id']}")
        except Exception as e:
            logger.error(f"❌ Failed to increment offer_views for fallback offer {best_fallback['offer_id']}: {str(e)}")

        logger.info(f"✅ Selected fallback offer: {best_fallback.get('title', 'Unknown')} with score {best_fallback['intent_match_score']}")
        return best_fallback

    except Exception as e:
        logger.exception(f"❌ Error in fetch_fallback_offer: {str(e)}")
        return None


def summarize_detected_intent(intent: Dict[str, Any], is_fallback: bool = False) -> str:
    """Generate a summary of the detected intent."""
    goal = intent.get("goal", "")
    if isinstance(goal, list):
        goal = ", ".join(goal)

    # Get the first category for the summary
    categories = intent.get("categories", ["software"])
    category_str = categories[0] if categories else "software"

    if is_fallback:
        return f"While we couldn't find specific {category_str} tools matching your exact query '{goal}', here's a trusted option from our available inventory that might be of interest:"
    else:
        return f"Here are {category_str} tools that match your goal: {goal}"


def build_followup_suggestions(
    intent: Dict[str, Any],
    offers: List[Dict[str, Any]],
    session_id: str
) -> List[Dict[str, Any]]:
    """
    Generate contextual follow-up suggestions based on recommended products.
    Each suggestion includes product mentions and their admesh links.
    """
    # Get the first category for suggestions
    categories = intent.get("categories", ["software"])
    category_str = categories[0].lower() if categories else "software"

    suggestions = []

    # Skip if no offers
    if not offers:
        logger.warning("⚠️ No offers available for followup suggestions")
        return []

    logger.info(
        f"🔍 Building followup suggestions for {len(offers)} offers in category: {category_str}")

    # Extract product titles and admesh links
    product_titles = []
    product_links = {}

    for offer in offers:
        title = offer.get("title", "")
        if title:
            product_titles.append(title)
            product_links[title] = offer.get("admesh_link", "")

    logger.info(f"📋 Product titles: {product_titles}")

    # 1️⃣ Feature comparison suggestion for top product
    if product_titles:
        top_product = product_titles[0]
        suggestions.append({
            "label": f"What features does {top_product} offer?",
            "query": f"{top_product} key features and benefits",
            "product_mentions": [top_product],
            "admesh_links": {top_product: product_links.get(top_product, "")},
            "session_id": session_id
        })

    # 2️⃣ Compare top 2 products
    if len(product_titles) >= 2:
        p1, p2 = product_titles[0], product_titles[1]
        suggestions.append({
            "label": f"Compare {p1} vs {p2}",
            "query": f"Compare {p1} vs {p2} for {category_str}",
            "product_mentions": [p1, p2],
            "admesh_links": {
                p1: product_links.get(p1, ""),
                p2: product_links.get(p2, "")
            },
            "session_id": session_id
        })

    # 3️⃣ Pricing suggestion for top product
    if product_titles:
        top_product = product_titles[0]
        suggestions.append({
            "label": f"{top_product} pricing plans",
            "query": f"{top_product} pricing and plans",
            "product_mentions": [top_product],
            "admesh_links": {top_product: product_links.get(top_product, "")},
            "session_id": session_id
        })

    # 4️⃣ Alternatives to second best product (if available)
    if len(product_titles) >= 2:
        second_product = product_titles[1]
        alternatives = [p for p in product_titles if p !=
                        second_product][:3]  # Top 3 alternatives

        suggestion = {
            "label": f"Alternatives to {second_product}",
            "query": f"Best alternatives to {second_product}",
            "product_mentions": alternatives,
            "admesh_links": {p: product_links.get(p, "") for p in alternatives},
            "session_id": session_id
        }
        suggestions.append(suggestion)

    # 5️⃣ Category exploration with top product
    if product_titles and category_str:
        top_product = product_titles[0]
        suggestions.append({
            "label": f"Best {category_str} tools like {top_product}",
            "query": f"Best {category_str} tools similar to {top_product}",
            "product_mentions": [top_product],
            "admesh_links": {top_product: product_links.get(top_product, "")},
            "session_id": session_id
        })

    logger.info(f"✅ Generated {len(suggestions)} followup suggestions")
    return suggestions


def save_query_session(
    query: str,
    intent: Dict[str, Any],
    model: str,
    agent_id: str,
    user_id: Optional[str],
    session_id: str
) -> None:
    """Save the query session to Firestore."""
    try:
        # Create session document
        sessions_ref = db.collection("sessions")
        session_data = {
            "session_id": session_id,
            "agent_id": agent_id,
            "user_id": user_id,
            "initial_query": query,
            "intent": intent,
            "model_used": model,
            "created_at": firestore.SERVER_TIMESTAMP,
            "last_updated": firestore.SERVER_TIMESTAMP,
            "followup_count": 0
        }

        # Save session
        sessions_ref.document(session_id).set(session_data)

    except Exception as e:
        logger.exception(f"Failed to save agent session: {str(e)}")
        # Continue execution even if saving fails
