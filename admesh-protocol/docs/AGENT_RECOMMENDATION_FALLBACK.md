# Agent Recommendation Fallback System

## Overview

The Agent Recommendation Fallback System provides a safety net when the primary recommendation algorithm fails to find relevant offers matching a user's query. Instead of returning empty results, the system intelligently selects a high-quality offer from the available inventory as a fallback recommendation.

## How It Works

### Primary Recommendation Flow
1. User submits a query
2. System detects intent (categories, keywords, etc.)
3. `fetch_active_offers()` searches for matching offers using:
   - Category matching
   - Keyword overlap
   - Semantic similarity (embeddings)
   - Trust scores
   - Payout values

### Fallback Mechanism
When the primary search returns no results:

1. **Fallback Trigger**: If `scored_offers` is empty, the system automatically calls `fetch_fallback_offer()`
2. **Fallback Selection**: Selects the best available offer based on:
   - **Trust Score (70%)**: Combined offer and brand trust scores
   - **Payout Score (30%)**: Normalized payout amount (max $100)
3. **Clear Indication**: Marks the offer as `is_fallback: true` with appropriate messaging

## Fallback Scoring Algorithm

```python
# Trust component (70% weight)
combined_trust = (offer_trust_score + brand_trust_score) / 2

# Payout component (30% weight)  
payout_score = min(payout_amount / 100, 1.0)

# Final fallback score
fallback_score = (0.7 * combined_trust) + (0.3 * payout_score)
```

## API Response Changes

### New Fields Added

#### AgentRecommendation Model
```python
class AgentRecommendation(BaseModel):
    # ... existing fields ...
    is_fallback: Optional[bool] = False
```

#### AgentRecommendResponse
```python
{
    "session_id": "sess_123",
    "intent": {...},
    "response": {
        "summary": "While we couldn't find specific tools matching your exact query...",
        "recommendations": [
            {
                "title": "ProductivityPro",
                "reason": "This offer is available in our inventory as a fallback recommendation...",
                "is_fallback": true,
                "intent_match_score": 0.8375,
                # ... other fields
            }
        ],
        "is_fallback": true,  # New field indicating fallback response
        "followup_suggestions": [...]
    }
}
```

## User Experience

### Normal Recommendations
- Summary: "Here are software tools that match your goal: [user_goal]"
- `is_fallback: false`
- Recommendations based on relevance matching

### Fallback Recommendations  
- Summary: "While we couldn't find specific [category] tools matching your exact query '[goal]', here's a trusted option from our available inventory that might be of interest:"
- `is_fallback: true`
- Single high-quality recommendation with clear fallback messaging

## Implementation Details

### Key Functions

#### `fetch_fallback_offer(is_test: bool = False)`
- Queries active offers without category/keyword filters
- Scores offers using trust + payout algorithm
- Returns single best offer with fallback metadata
- Increments appropriate view counters (test/production)

#### `summarize_detected_intent(intent, is_fallback=False)`
- Generates appropriate summary text
- Handles fallback messaging when `is_fallback=True`

### Error Handling
- Returns `None` if no active offers exist
- Skips offers with missing/invalid product data
- Gracefully handles database errors
- Logs all fallback operations for monitoring

## Benefits

1. **No Empty Results**: Users always receive a recommendation when inventory exists
2. **Quality Assurance**: Fallback offers are selected based on trust and value
3. **Transparency**: Clear indication that it's a fallback recommendation
4. **Inventory Utilization**: Helps surface quality offers that might not match specific queries
5. **User Retention**: Keeps users engaged even when exact matches aren't available

## Monitoring & Analytics

The system tracks:
- Fallback trigger frequency
- Fallback offer performance
- View counters for fallback offers (separate test/production)
- User engagement with fallback recommendations

## Testing

Run the fallback tests:
```bash
python -m pytest tests/test_agent_recommendation_fallback.py -v
```

Run the demo:
```bash
python test_fallback_demo.py
```

## Configuration

The fallback scoring weights can be adjusted:
- Trust weight: Currently 70%
- Payout weight: Currently 30%
- Maximum payout normalization: $100

## Future Enhancements

1. **Relevance Checking**: Add basic relevance scoring even for fallback offers
2. **Multiple Fallbacks**: Return 2-3 fallback options instead of just one
3. **Category Preferences**: Prefer fallback offers from related categories
4. **User History**: Consider user's previous interactions for fallback selection
5. **A/B Testing**: Test different fallback messaging and selection algorithms
